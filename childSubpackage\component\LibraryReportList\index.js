import {getReportPageListApi, addBevHis} from '../../../service/industryApi';
import {hasPrivile} from '../../../utils/route';

const app = getApp();

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 请求参数
    requestParams: {
      type: Object,
      value: {
        type: 'hdzk'
      }
    },
    // 容器高度
    containerHeight: {
      type: Number,
      value: 600
    },
    // 是否自动检查VIP状态
    autoCheckVip: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 用户状态
    isLogin: false,
    isVip: false,
    showVipPage: false,

    // 研报列表
    reportList: [],

    // 总数据量
    totalCount: 0,

    // API请求函数
    RequestUrlFn: getReportPageListApi,
    // 请求参数（从 properties 复制过来）
    requestParams: {}
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
      this.initComponent();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initComponent() {
      const {login} = app.globalData;
      this.setData({
        isLogin: login,
        requestParams: this.properties.requestParams || {}
      });

      if (this.properties.autoCheckVip) {
        this.checkVipStatus();
      }
    },

    /**
     * 检查VIP状态
     */
    async checkVipStatus() {
      this.setData({
        isVip: false,
        showVipPage: true
      });
      await this.getFirstReportData();
      return;
      if (!this.data.isLogin) {
        this.setData({
          isVip: false,
          showVipPage: true
        });
        await this.getFirstReportData();
        return;
      }

      try {
        const vipStatus = await hasPrivile({
          packageType: true
        });

        const isVip = vipStatus !== '游客' && vipStatus !== '普通VIP';

        this.setData({
          isVip,
          showVipPage: !isVip
        });

        if (!isVip) {
          await this.getFirstReportData();
        }
      } catch (error) {
        console.error('组件检查VIP状态失败:', error);
        this.setData({
          isVip: false,
          showVipPage: true
        });
        await this.getFirstReportData();
      }
    },

    /**
     * 获取第一条研报数据（用于非VIP用户展示）
     */
    async getFirstReportData() {
      const requestParams = {
        ...(this.properties.requestParams || {}),
        page_index: 1,
        page_size: 1
      };

      try {
        const response = await getReportPageListApi(requestParams);

        console.log('getFirstReportData API响应:', {
          hasResponse: !!response,
          listLength: response?.list?.length || 0,
          total: response?.total || 0
        });

        if (response && response.list && response.list.length > 0) {
          const transformedList = this.transformReportData(response.list);
          this.setData({
            reportList: transformedList,
            totalCount: response.total || 0
          });

          // 向父组件传递数据变化事件，包含total信息
          this.triggerEvent('datachange', {
            list: transformedList,
            total: response.total || 0,
            hasMore: false,
            isEmpty: false
          });

          console.log(
            '使用API数据，数量:',
            transformedList.length,
            'total:',
            response.total
          );
        } else {
          console.log('API返回空数据');
          this.setData({
            reportList: []
          });

          // 向父组件传递空数据事件
          this.triggerEvent('datachange', {
            list: [],
            total: 0,
            hasMore: false,
            isEmpty: true
          });
        }
      } catch (error) {
        console.error('组件获取第一条研报数据失败:', error);
        this.setData({
          reportList: []
        });

        // 向父组件传递错误状态
        this.triggerEvent('datachange', {
          list: [],
          total: 0,
          hasMore: false,
          isEmpty: true
        });
      }
    },

    /**
     * 错误处理
     */
    onError(e) {
      console.error('撼地智库组件API请求失败:', e.detail);

      // 清空数据
      this.setData({
        reportList: []
      });

      // 向父组件传递错误事件
      this.triggerEvent('error', e.detail);

      // 显示错误提示
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      });
    },

    /**
     * 研报点击事件
     */
    onReportClick(e) {
      const {item} = e.detail;

      // 向父组件传递点击事件
      this.triggerEvent('reportclick', {item});
    },

    /**
     * VIP支付成功回调
     */
    onVipPaySuccess() {
      console.log('组件收到VIP支付成功回调');
      // 重新检查VIP状态
      this.checkVipStatus();

      // 向父组件传递支付成功事件
      this.triggerEvent('vippaysuccess');
    },

    /**
     * 列表数据变化回调（内部处理）
     */
    onListDataChange(e) {
      const {list = [], total = 0, hasMore = false, isEmpty = false} = e.detail;

      // 转换API数据格式为ReportCard组件期望的格式
      const transformedList = this.transformReportData(list);

      // 更新组件数据
      this.setData({
        reportList: transformedList
      });

      // 向父组件传递处理后的数据
      this.triggerEvent('datachange', {
        list: transformedList,
        total: total,
        hasMore: hasMore,
        isEmpty: isEmpty
      });
    },

    /**
     * 研报点击事件（内部处理）
     */
    onReportClick(e) {
      const {item} = e.detail;
      console.log('组件内部研报点击:', item);

      // 检查VIP状态
      if (!this.data.isVip) {
        wx.showToast({
          title: '请购买VIP后查看',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // VIP用户可以查看，显示操作选择
      this.showReportActionSheet(item);
    },

    /**
     * 显示研报操作选择
     */
    showReportActionSheet(reportItem) {
      const itemList = ['在线预览', '下载到本地'];
      wx.showActionSheet({
        itemList,
        success: res => {
          switch (res.tapIndex) {
            case 0:
              this.previewReport(reportItem);
              break;
            case 1:
              this.downloadReport(reportItem);
              break;
          }
        },
        fail: () => {
          console.log('用户取消操作');
        }
      });
    },

    /**
     * 在线预览研报
     */
    previewReport(reportItem) {
      console.log('预览研报:', reportItem);

      // 向父组件传递预览事件
      this.triggerEvent('reportpreview', {item: reportItem});

      // 默认显示提示
      wx.showToast({
        title: `预览: ${reportItem.title}`,
        icon: 'none'
      });
    },

    /**
     * 下载研报到本地
     */
    downloadReport(reportItem) {
      console.log('下载研报:', reportItem);

      // 向父组件传递下载事件
      this.triggerEvent('reportdownload', {item: reportItem});

      // 默认显示提示
      wx.showToast({
        title: `下载: ${reportItem.title}`,
        icon: 'none'
      });
    },

    /**
     * 转换API数据格式
     */
    transformReportData(apiList) {
      if (!Array.isArray(apiList)) {
        return [];
      }

      return apiList.map(item => {
        const tags = [];
        if (item.chains && Array.isArray(item.chains)) {
          const chainNames = item.chains
            .slice(0, 2)
            .map(chain => chain?.name)
            .filter(Boolean);
          tags.push(...chainNames);
        }
        return {
          id: item.id,
          title: item.report_name || '--',
          size: item.file_size || '-',
          tags: tags,
          organization: item.publish_org,
          date: this.formatDate(item.publish_time),
          pdfUrl: item.report_oss_url || '',
          imgTit:
            item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
          page_num: item.page_num,
          originalData: item
        };
      });
    },

    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString) return '未知日期';
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      } catch (error) {
        return dateString;
      }
    }
  }
});
