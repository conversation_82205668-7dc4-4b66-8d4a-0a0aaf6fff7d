import {getReportPageListApi, addBevHis} from '../../../service/industryApi';
import {getHeight} from '../../../utils/height';

const app = getApp();

// Mock 数据
const MOCK_DATA = {
  // 搜索历史关键词
  searchHistory: [
    '新能源汽车',
    '人工智能',
    '半导体',
    '生物医药',
    '新材料',
    '5G通信',
    '云计算',
    '区块链'
  ],
  // 浏览历史企业
  browsingHistory: [
    {
      enterprise_name: '比亚迪股份有限公司',
      enterprise_id: '1001',
      create_time: '01-15'
    },
    {
      enterprise_name: '宁德时代新能源科技股份有限公司',
      enterprise_id: '1002',
      create_time: '01-14'
    },
    {
      enterprise_name: '华为技术有限公司',
      enterprise_id: '1003',
      create_time: '01-13'
    },
    {
      enterprise_name: '腾讯科技（深圳）有限公司',
      enterprise_id: '1004',
      create_time: '01-12'
    },
    {
      enterprise_name: '阿里巴巴（中国）有限公司',
      enterprise_id: '1005',
      create_time: '01-11'
    },
    {
      enterprise_name: '小米科技有限责任公司',
      enterprise_id: '1006',
      create_time: '01-10'
    },
    {
      enterprise_name: '京东方科技集团股份有限公司',
      enterprise_id: '1007',
      create_time: '01-09'
    },
    {
      enterprise_name: '中芯国际集成电路制造有限公司',
      enterprise_id: '1008',
      create_time: '01-08'
    }
  ],
  // 搜索结果数据 - 按类型分类
  searchResults: {
    report: [
      {
        id: 'report_1',
        title: '2024年新能源汽车产业报告',
        description:
          '详细分析新能源汽车行业发展趋势，包含市场规模、技术发展等内容。',
        type: '产业报告'
      },
      {
        id: 'report_2',
        title: '人工智能技术发展白皮书',
        description: '全面解读AI技术在各行业的应用现状和未来发展方向。',
        type: '技术报告'
      },
      {
        id: 'report_3',
        title: '半导体行业深度调研',
        description: '深入分析半导体产业链上下游企业发展状况和投资机会。',
        type: '调研报告'
      }
    ],
    chart: [
      {
        id: 'chart_1',
        title: '新能源汽车销量趋势图',
        description: '展示近5年新能源汽车销量变化趋势和预测数据。',
        type: '趋势图表'
      },
      {
        id: 'chart_2',
        title: 'AI企业分布热力图',
        description: '全国AI企业地理分布情况和产业集群分析图表。',
        type: '分布图表'
      }
    ],
    library: [
      {
        id: 'library_1',
        title: '撼地智库：产业链分析',
        description: '基于大数据的产业链深度分析，提供专业的投资建议。',
        type: '智库分析'
      },
      {
        id: 'library_2',
        title: '撼地智库：市场洞察',
        description: '结合AI算法的市场趋势预测和行业发展洞察报告。',
        type: '市场分析'
      },
      {
        id: 'library_3',
        title: '撼地智库：企业画像',
        description: '多维度企业画像分析，助力精准招商和投资决策。',
        type: '企业分析'
      }
    ]
  }
};

Page({
  data: {
    // 搜索相关
    inputShowed: true,
    report_name: ' ', // 写死测试内容

    // 历史数据
    historyList: [...MOCK_DATA.searchHistory],
    browsingHistory: [...MOCK_DATA.browsingHistory],

    // 搜索结果相关
    currentTab: 'report', // 当前选中的tab
    searchResults: MOCK_DATA.searchResults,
    currentResults: [...MOCK_DATA.searchResults.report], // 当前显示的结果

    // 撼地智库相关数据
    libraryReportList: [], // 撼地智库研报列表，初始为空
    libraryTotal: 0, // 撼地智库研报总数
    libraryRequestParams: {
      type: 'chainMap' // 热门研报，与thinkTankList保持一致
    },
    RequestUrlFn: getReportPageListApi, // API 请求函数
    listScrollHeight: 600, // 列表滚动高度

    // 配置
    scrollHeight: 600,
    isLogin: app.isLogin(),

    // 高度计算相关
    isHeightCalculated: false, // 是否已计算过高度
    searchScrollHeight: 600, // 搜索结果区域高度
    tabsHeight: 0 // tabs区域高度
  },

  onLoad(options) {
    console.log('页面加载参数:', options);
    this.initPage(options);
  },

  onShow() {
    this.updateLoginStatus();
    // 重新计算高度
    this.calculateAllHeights();
  },
  onReady() {
    console.log('页面渲染完成');
    // 页面渲染完成后计算高度
    this.calculateAllHeights();
  },

  /**
   * 初始化页面
   * @param {Object} options - 页面参数
   */
  initPage(options = {}) {
    // 检查来源页面和参数
    const {type, from} = options;

    // 如果从 thinkTankList 页面进来，且有输入内容，默认选中撼地智库
    if (from === 'thinkTankList' || type === 'hdzk') {
      this.setData({
        currentTab: 'library',
        currentResults: [...this.data.searchResults.library]
      });
      console.log('从撼地智库页面进入，默认选中撼地智库tab');
    }
  },

  /**
   * 更新登录状态
   */
  updateLoginStatus() {
    const {login} = app.globalData;
    this.setData({
      isLogin: login
    });
  },

  /**
   * 清空搜索输入
   */
  onClear() {
    this.setData({
      report_name: ''
    });
  },

  /**
   * 输入框失去焦点
   */
  onBlur() {
    console.log('输入框失去焦点');
  },

  /**
   * 输入框内容变化
   */
  onInput(e) {
    const keyword = e.detail.value;
    this.setData({
      report_name: keyword
    });
  },

  /**
   * 确认搜索
   */
  onConfirm(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      this.addToSearchHistory(keyword);
    }
  },

  /**
   * 添加到搜索历史
   * @param {string} keyword - 搜索关键词
   */
  addToSearchHistory(keyword) {
    const {historyList} = this.data;
    const newHistoryList = [...historyList];

    // 如果已存在，先移除
    const existIndex = newHistoryList.indexOf(keyword);
    if (existIndex !== -1) {
      if (existIndex === 0) return; // 已在第一位，无需操作
      newHistoryList.splice(existIndex, 1);
    }

    // 添加到第一位
    newHistoryList.unshift(keyword);

    // 限制历史记录数量
    if (newHistoryList.length > 10) {
      newHistoryList.pop();
    }

    this.setData({
      historyList: newHistoryList
    });
  },

  /**
   * 点击搜索历史
   */
  historyTap(e) {
    const keyword = e.target.dataset.item;
    this.setData({
      inputShowed: true,
      report_name: keyword
    });
  },

  /**
   * 点击删除图标
   */
  handleIcon(e) {
    const type = e.currentTarget.dataset.index;

    switch (type) {
      case 'a':
        this.clearSearchHistory();
        break;
      default:
        break;
    }
  },

  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    wx.showModal({
      title: '删除搜索',
      content: '确定要删除最近搜索?',
      success: res => {
        if (res.confirm) {
          this.setData({
            historyList: []
          });
        }
      }
    });
  },

  /**
   * 处理AI图片点击事件
   */
  handleClick(e) {
    const {isLogin} = this.data;
    const type = e.currentTarget.dataset?.type;

    if (!isLogin) {
      this.redirectToLogin();
      return;
    }

    this.handleAIClick(type);
  },

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    app.route(this, '/pages/login/login');
  },

  /**
   * 处理AI点击
   */
  handleAIClick(type) {
    switch (type) {
      case 'ai':
        // TODO: 后续实现AI报告页面跳转
        // app.route(this, '/childSubpackage/pages/aiReport/index');
        wx.showToast({
          title: 'AI功能开发中',
          icon: 'none'
        });
        break;
      default:
        break;
    }
  },

  /**
   * 浏览历史点击事件
   * @param {Object} e - 事件对象
   */
  goDetail(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击浏览历史:', item);

    // TODO: 后续改造为新的逻辑
    // 可能的实现：
    // 1. 跳转到企业详情页
    // 2. 添加到搜索历史
    // 3. 其他业务逻辑
  },

  /**
   * Tab 切换事件
   * @param {Object} e - 事件对象
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    const {searchResults} = this.data;

    this.setData({
      currentTab: tab,
      currentResults: [...searchResults[tab]]
    });

    console.log(`切换到 ${tab} tab，结果数量：${searchResults[tab].length}`);

    // 如果切换到撼地智库tab，重新计算高度
    if (tab === 'library') {
      setTimeout(() => {
        this.calculateLibraryHeight();
      }, 200); // 等待DOM更新
    }
  },

  /**
   * 搜索结果点击事件
   * @param {Object} e - 事件对象
   */
  onResultClick(e) {
    const item = e.currentTarget.dataset.item;
    const {currentTab} = this.data;

    console.log('点击搜索结果:', item);

    // 根据不同tab处理不同逻辑
    switch (currentTab) {
      case 'report':
        this.handleReportClick(item);
        break;
      case 'chart':
        this.handleChartClick(item);
        break;
      case 'library':
        this.handleLibraryClick(item);
        break;
      default:
        console.log('未知tab类型:', currentTab);
        break;
    }
  },

  /**
   * 处理报告点击
   */
  handleReportClick(item) {
    wx.showToast({
      title: `点击了报告: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到报告详情页
  },

  /**
   * 处理图表点击
   */
  handleChartClick(item) {
    wx.showToast({
      title: `点击了图表: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到图表详情页
  },

  /**
   * 处理智库点击
   */
  handleLibraryClick(item) {
    wx.showToast({
      title: `点击了智库: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到智库详情页
  },

  /**
   * 撼地智库数据变化回调
   */
  onLibraryDataChange(e) {
    console.log('撼地智库数据变化:', e.detail);
    const {list = [], total = 0, hasMore = false, isEmpty = false} = e.detail;

    this.setData({
      libraryReportList: list,
      libraryTotal: total // 保存总数，用于显示
    });

    console.log('撼地智库数据更新:', {
      listLength: list.length,
      total: total,
      hasMore: hasMore,
      isEmpty: isEmpty
    });
  },

  /**
   * 撼地智库API请求错误处理
   */
  onLibraryError(e) {
    console.error('撼地智库API请求失败:', e.detail);
    // 错误处理已在组件内部完成
  },

  /**
   * 撼地智库研报点击事件
   */
  onLibraryReportClick(e) {
    const {item} = e.detail;
    console.log('点击撼地智库研报:', item);

    // 显示操作选择弹窗
    const itemList = ['在线预览', '下载到本地'];
    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(item);
            break;
          case 1:
            this.downloadReport(item);
            break;
        }
      },
      fail: () => {
        console.log('用户取消操作');
      }
    });
  },

  /**
   * 在线预览研报
   */
  previewReport(reportItem) {
    wx.showToast({
      title: `预览: ${reportItem.title}`,
      icon: 'none'
    });
    // TODO: 实现真实的预览功能
  },

  /**
   * 下载研报到本地
   */
  downloadReport(reportItem) {
    wx.showToast({
      title: `下载: ${reportItem.title}`,
      icon: 'none'
    });
    // TODO: 实现真实的下载功能
  },

  /**
   * 一次性计算所有高度
   */
  calculateAllHeights() {
    // 延迟执行，确保DOM渲染完成
    setTimeout(() => {
      // 根据当前状态选择不同的计算策略
      if (this.data.report_name && this.data.currentTab === 'library') {
        // 撼地智库tab的高度计算
        this.calculateLibraryHeight();
      } else {
        // 其他情况的高度计算
        this.calculateGeneralHeight();
      }
    }, 100);
  },

  /**
   * 计算撼地智库的高度 - 简化版本，参考thinkTankList
   */
  calculateLibraryHeight() {
    try {
      // 延迟执行，确保DOM渲染完成
      setTimeout(() => {
        const systemInfo = wx.getSystemInfoSync();
        const screenHeight = systemInfo.windowHeight;

        // 预估各部分高度（单位：px）
        const searchHeight = 80; // 搜索框高度
        const tabsHeight = 80; // tabs区域高度
        const companyNumHeight = 60; // 统计信息高度
        const padding = 40; // 各种边距

        // 计算列表可用高度
        const listScrollHeight =
          screenHeight - searchHeight - tabsHeight - companyNumHeight - padding;

        this.setData({
          listScrollHeight: Math.max(listScrollHeight, 300),
          isHeightCalculated: true
        });

        console.log('撼地智库高度计算完成（简化版）:', {
          screenHeight,
          searchHeight,
          tabsHeight,
          companyNumHeight,
          padding,
          listScrollHeight: Math.max(listScrollHeight, 300)
        });
      }, 300);
    } catch (error) {
      console.error('撼地智库高度计算失败:', error);
      // 使用默认高度
      this.setData({
        listScrollHeight: 400,
        isHeightCalculated: true
      });
    }
  },

  /**
   * 计算通用高度
   */
  calculateGeneralHeight() {
    try {
      getHeight(this, ['.searchs'], data => {
        const {screeHeight, res} = data;

        // 安全获取搜索框高度
        const searchHeight =
          res && res[0] && res[0].height ? res[0].height : 80;

        // 计算历史记录区域高度
        const scrollHeight = screeHeight - searchHeight;

        this.setData({
          scrollHeight: Math.max(scrollHeight, 300),
          isHeightCalculated: true
        });

        console.log('通用高度计算完成:', {
          screeHeight,
          searchHeight,
          scrollHeight: Math.max(scrollHeight, 300),
          resLength: res ? res.length : 0
        });
      });
    } catch (error) {
      console.error('通用高度计算失败:', error);
      // 使用默认高度
      this.setData({
        scrollHeight: 400,
        isHeightCalculated: true
      });
    }
  }
});
