<!-- 撼地智库完整页面组件 -->
<view class="library-report-list">
  <!-- 列表页面 -->
  <view class="list-container">
    <view class="company_num"> </view>

    <!-- VIP用户：显示完整列表 -->
    <view wx:if="{{isVip}}" class="vip-container">
      <view class="card-box" style="height: {{containerHeight}}px;">
        <refresh-scroll
          id="libraryRefreshScroll"
          container-height="{{containerHeight}}"
          request-url="{{RequestUrlFn}}"
          requestParams="{{requestParams}}"
          empty-text="暂无研报数据"
          empty-tip=""
          bind:datachange="onListDataChange"
          bind:error="onError"
          custom-wrapper-class="custom-wrapper-class"
          requestType="pageIndex"
        >
          <view slot="content" class="list_wrp">
            <ReportCard
              report-list="{{reportList}}"
              bindreportclick="onReportClick"
            />
          </view>
        </refresh-scroll>
      </view>
    </view>

    <!-- 非VIP用户：显示一条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{containerHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <!-- 显示第一条研报数据 -->
      <view wx:if="{{reportList.length > 0}}" class="first-report-container">
        <SingleReportCard
          report-data="{{reportList[0]}}"
          bindreportclick="onReportClick"
        />
      </view>
      <!-- 剩余信息提示 -->
      <view wx:if="{{showVipPage && totalCount > 1}}" class="remaining-info">
        <text class="remaining-text">剩余 {{totalCount - 1}} 条相关信息</text>
      </view>
      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bind:paySuccess="onVipPaySuccess">
        <!-- 可以在这里添加额外的提示内容 -->
      </vip-page>
    </scroll-view>
  </view>
</view>
